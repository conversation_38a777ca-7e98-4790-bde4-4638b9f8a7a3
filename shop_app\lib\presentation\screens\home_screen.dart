import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/category.dart';
import '../../data/repositories/product_repository.dart';
import '../widgets/banner_widget.dart';
import '../widgets/category_item.dart';
import '../widgets/product_card.dart';
import '../widgets/section_header.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();
  int _cartItemCount = 3;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            _buildAppBar(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(height: AppConstants.paddingSmall),
                    _buildSearchBar(),
                    const SizedBox(height: AppConstants.paddingSmall),
                    _buildBanner(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildCategoriesSection(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildFeaturedProductsSection(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildBestSellersSection(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildRecentlyViewedSection(),
                    const SizedBox(
                      height: 100,
                    ), // Bottom padding for navigation
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'logo',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontFamily: 'Pacifico',
              color: AppColors.primary,
            ),
          ),
          Row(
            children: [
              _buildIconButton(
                icon: Theme.of(context).brightness == Brightness.dark
                    ? Icons.light_mode
                    : Icons.dark_mode,
                onPressed: () {
                  // Toggle theme functionality would go here
                },
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              _buildIconButton(
                icon: Icons.search,
                onPressed: () {
                  // Search functionality would go here
                },
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              _buildIconButton(
                icon: Icons.favorite_border,
                onPressed: () {
                  // Wishlist functionality would go here
                },
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              _buildCartButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 32,
        height: 32,
        decoration: const BoxDecoration(shape: BoxShape.circle),
        child: Icon(
          icon,
          size: 20,
          color: Theme.of(context).textTheme.bodyLarge?.color,
        ),
      ),
    );
  }

  Widget _buildCartButton() {
    return GestureDetector(
      onTap: () {
        // Cart functionality would go here
      },
      child: SizedBox(
        width: 32,
        height: 32,
        child: Stack(
          children: [
            Center(
              child: Icon(
                Icons.shopping_cart_outlined,
                size: 20,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
            if (_cartItemCount > 0)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  width: 18,
                  height: 18,
                  decoration: const BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      _cartItemCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search products...',
          prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
          hintStyle: const TextStyle(color: AppColors.textSecondary),
        ),
        onSubmitted: (value) {
          // Search functionality would go here
        },
      ),
    );
  }

  Widget _buildBanner() {
    return const BannerWidget(
      title: 'Summer Sale',
      subtitle: 'Up to 50% off on selected items',
      buttonText: 'Shop Now',
      imageUrl:
          'https://readdy.ai/api/search-image?query=modern%20shopping%20app%20banner%2C%20colorful%20summer%20sale%20promotion%2C%20fashion%20and%20electronics%20products%2C%20vibrant%20colors%2C%20professional%20e-commerce%20design%2C%20high%20quality&width=375&height=160&seq=1&orientation=landscape',
    );
  }

  Widget _buildCategoriesSection() {
    final categories = CategoryData.getCategories();

    return Column(
      children: [
        SectionHeader(
          title: 'Categories',
          actionText: 'View All',
          onActionPressed: () {
            // Navigate to categories screen
          },
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        SizedBox(
          height: 100,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
            ),
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            separatorBuilder: (context, index) =>
                const SizedBox(width: AppConstants.paddingMedium),
            itemBuilder: (context, index) {
              final category = categories[index];
              return CategoryItem(
                category: category,
                onTap: () {
                  // Navigate to category products
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedProductsSection() {
    final products = ProductRepository.getFeaturedProducts();

    return Column(
      children: [
        SectionHeader(
          title: 'Featured Products',
          actionText: 'View All',
          onActionPressed: () {
            // Navigate to featured products screen
          },
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
          ),
          child: _buildResponsiveGrid(products),
        ),
      ],
    );
  }

  Widget _buildBestSellersSection() {
    final products = ProductRepository.getBestSellers();

    return Column(
      children: [
        SectionHeader(
          title: 'Best Sellers',
          actionText: 'View All',
          onActionPressed: () {
            // Navigate to best sellers screen
          },
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
          ),
          child: _buildResponsiveGrid(products),
        ),
      ],
    );
  }

  Widget _buildRecentlyViewedSection() {
    final products = ProductRepository.getRecentlyViewed();

    return Column(
      children: [
        SectionHeader(
          title: 'Recently Viewed',
          actionText: 'Clear All',
          onActionPressed: () {
            // Clear recently viewed
          },
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        SizedBox(
          height: 200,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
            ),
            scrollDirection: Axis.horizontal,
            itemCount: products.length,
            separatorBuilder: (context, index) =>
                const SizedBox(width: AppConstants.paddingSmall),
            itemBuilder: (context, index) {
              final product = products[index];
              return SizedBox(
                width: 140,
                child: ProductCard(
                  product: product,
                  isCompact: true,
                  onTap: () {
                    // Navigate to product detail
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      height: AppConstants.bottomNavHeight,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildBottomNavItem(
            icon: Icons.home,
            label: 'Home',
            isSelected: true,
            onTap: () {},
          ),
          _buildBottomNavItem(
            icon: Icons.apps,
            label: 'Categories',
            isSelected: false,
            onTap: () {},
          ),
          _buildBottomNavItem(
            icon: Icons.favorite_border,
            label: 'Wishlist',
            isSelected: false,
            onTap: () {},
          ),
          _buildBottomNavItem(
            icon: Icons.shopping_cart_outlined,
            label: 'Cart',
            isSelected: false,
            onTap: () {},
          ),
          _buildBottomNavItem(
            icon: Icons.person_outline,
            label: 'Profile',
            isSelected: false,
            onTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavItem({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 24,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: isSelected ? AppColors.primary : AppColors.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveGrid(List<dynamic> products) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Determine grid properties based on screen size
    int crossAxisCount;
    double childAspectRatio;

    if (screenWidth > 1200) {
      // Desktop
      crossAxisCount = AppConstants.gridCrossAxisCountDesktop;
      childAspectRatio = AppConstants.gridChildAspectRatioTablet;
    } else if (screenWidth > 600) {
      // Tablet
      crossAxisCount = AppConstants.gridCrossAxisCountTablet;
      childAspectRatio = AppConstants.gridChildAspectRatioTablet;
    } else {
      // Mobile
      crossAxisCount = AppConstants.gridCrossAxisCount;
      childAspectRatio = AppConstants.gridChildAspectRatio;
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: AppConstants.gridSpacing,
        mainAxisSpacing: AppConstants.gridSpacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return ProductCard(
          product: product,
          onTap: () {
            // Navigate to product detail
          },
          onFavoritePressed: () {
            // Toggle favorite
          },
          onAddToCart: () {
            _showAddToCartModal(product);
          },
        );
      },
    );
  }

  void _showAddToCartModal(product) {
    setState(() {
      _cartItemCount++;
    });

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(AppConstants.radiusXLarge),
          ),
        ),
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Added to Cart',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.borderLight,
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusLarge,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      AppConstants.radiusLarge,
                    ),
                    child: Image.network(
                      product.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const Icon(
                        Icons.image_not_supported,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w500),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Quantity: 1',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        product.formattedPrice,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: AppColors.primary),
                      foregroundColor: AppColors.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppConstants.radiusButton,
                        ),
                      ),
                    ),
                    child: const Text('Continue Shopping'),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // Navigate to cart
                    },
                    child: const Text('View Cart'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
