import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/product.dart';

class ProductCard extends StatefulWidget {
  final Product product;
  final VoidCallback? onTap;
  final VoidCallback? onFavoritePressed;
  final VoidCallback? onAddToCart;
  final bool isCompact;
  final double? width;
  final double? height;

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.onFavoritePressed,
    this.onAddToCart,
    this.isCompact = false,
    this.width,
    this.height,
  });

  @override
  State<ProductCard> createState() => _ProductCardState();
}

class _ProductCardState extends State<ProductCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
    widget.onTap?.call();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    // Responsive sizing
    final cardWidth = widget.width ?? _getResponsiveWidth(screenWidth);
    final cardHeight = widget.height ?? _getResponsiveHeight(screenWidth);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: Container(
              width: cardWidth,
              height: cardHeight,
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(AppConstants.radiusCard),
                border: Border.all(
                  color: AppColors.border.withOpacity(0.1),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(_isPressed ? 0.15 : 0.08),
                    blurRadius: _isPressed ? 12 : 8,
                    offset: Offset(0, _isPressed ? 4 : 2),
                    spreadRadius: _isPressed ? 1 : 0,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildImageSection(context, isTablet),
                  _buildContentSection(context, isTablet),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  double _getResponsiveWidth(double screenWidth) {
    if (widget.isCompact) {
      return screenWidth > 600 ? 160 : 140;
    }
    return double.infinity;
  }

  double _getResponsiveHeight(double screenWidth) {
    if (widget.isCompact) {
      return screenWidth > 600 ? 220 : 200;
    }
    return screenWidth > 600 ? 280 : 240;
  }

  Widget _buildImageSection(BuildContext context, bool isTablet) {
    final imageHeight = widget.isCompact
        ? (isTablet ? 120.0 : 100.0)
        : (isTablet ? 160.0 : 140.0);

    return Container(
      height: imageHeight,
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(AppConstants.radiusCard),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(AppConstants.radiusCard),
              ),
              child: CachedNetworkImage(
                imageUrl: widget.product.imageUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: AppColors.borderLight,
                  child: Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.primary,
                      ),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  color: AppColors.borderLight,
                  child: Icon(
                    Icons.image_not_supported,
                    color: AppColors.textSecondary,
                    size: isTablet ? 32 : 24,
                  ),
                ),
              ),
            ),
          ),

          // Badges
          Positioned(
            top: AppConstants.paddingSmall,
            left: AppConstants.paddingSmall,
            child: Row(
              children: [
                if (widget.product.discountPercentage != null)
                  _buildBadge(
                    '${widget.product.discountPercentage}% OFF',
                    AppColors.primary,
                    isTablet,
                  ),
                if (widget.product.isNew)
                  Padding(
                    padding: EdgeInsets.only(
                      left: widget.product.discountPercentage != null ? 4 : 0,
                    ),
                    child: _buildBadge('NEW', AppColors.success, isTablet),
                  ),
              ],
            ),
          ),

          // Favorite Button
          if (!widget.isCompact)
            Positioned(
              top: AppConstants.paddingSmall,
              right: AppConstants.paddingSmall,
              child: _buildFavoriteButton(isTablet),
            ),
        ],
      ),
    );
  }

  Widget _buildFavoriteButton(bool isTablet) {
    final buttonSize = isTablet ? 32.0 : 28.0;
    final iconSize = isTablet ? 18.0 : 16.0;

    return GestureDetector(
      onTap: widget.onFavoritePressed,
      child: Container(
        width: buttonSize,
        height: buttonSize,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          widget.product.isFavorite ? Icons.favorite : Icons.favorite_border,
          size: iconSize,
          color: widget.product.isFavorite
              ? AppColors.primary
              : AppColors.textSecondary,
        ),
      ),
    );
  }

  Widget _buildContentSection(BuildContext context, bool isTablet) {
    final contentPadding = isTablet
        ? AppConstants.paddingMedium
        : AppConstants.paddingSmall;

    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(contentPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!widget.isCompact) ...[
              _buildRating(isTablet),
              SizedBox(height: 4),
            ],
            Expanded(child: _buildTitle(context, isTablet)),
            SizedBox(height: widget.isCompact ? 4 : 8),
            _buildPriceSection(context, isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildBadge(String text, Color color, bool isTablet) {
    final fontSize = isTablet ? 11.0 : 10.0;
    final padding = isTablet
        ? const EdgeInsets.symmetric(horizontal: 8, vertical: 3)
        : const EdgeInsets.symmetric(horizontal: 6, vertical: 2);

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(isTablet ? 6 : 4),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        text,
        style: TextStyle(
          color: Colors.white,
          fontSize: fontSize,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildRating(bool isTablet) {
    final starSize = isTablet ? 14.0 : 12.0;
    final textSize = isTablet ? 11.0 : 10.0;

    return Row(
      children: [
        ...List.generate(5, (index) {
          if (index < widget.product.rating.floor()) {
            return Icon(Icons.star, size: starSize, color: AppColors.rating);
          } else if (index < widget.product.rating) {
            return Icon(
              Icons.star_half,
              size: starSize,
              color: AppColors.rating,
            );
          } else {
            return Icon(
              Icons.star_border,
              size: starSize,
              color: AppColors.rating,
            );
          }
        }),
        const SizedBox(width: 4),
        Text(
          widget.product.rating.toString(),
          style: TextStyle(fontSize: textSize, color: AppColors.textSecondary),
        ),
      ],
    );
  }

  Widget _buildTitle(BuildContext context, bool isTablet) {
    final fontSize = isTablet ? 14.0 : 12.0;

    return Text(
      widget.product.name,
      style: Theme.of(context).textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w500,
        fontSize: fontSize,
      ),
      maxLines: widget.isCompact ? 2 : 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildPriceSection(BuildContext context, bool isTablet) {
    final priceSize = isTablet ? 14.0 : 12.0;
    final originalPriceSize = isTablet ? 11.0 : 10.0;
    final buttonSize = isTablet ? 32.0 : 28.0;
    final iconSize = isTablet ? 18.0 : 16.0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.product.formattedPrice,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: priceSize,
                ),
              ),
              if (widget.product.hasDiscount)
                Text(
                  widget.product.formattedOriginalPrice,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    decoration: TextDecoration.lineThrough,
                    color: AppColors.textSecondary,
                    fontSize: originalPriceSize,
                  ),
                ),
            ],
          ),
        ),
        if (!widget.isCompact && widget.onAddToCart != null)
          GestureDetector(
            onTap: widget.onAddToCart,
            child: Container(
              width: buttonSize,
              height: buttonSize,
              decoration: BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.3),
                    blurRadius: 6,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Icon(Icons.add, size: iconSize, color: Colors.white),
            ),
          ),
      ],
    );
  }
}
